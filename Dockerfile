# ---------- 1. 选择最小 base ----------
FROM nvidia/cuda:12.4.1-cudnn-runtime-ubuntu22.04
ENV LANG=C.UTF-8 LC_ALL=C.UTF-8

ENV DEBIAN_FRONTEN=noninteractive
ENV PYTHONUNBUFFERED=1
SHELL ["/bin/bash", "--login", "-c"]

RUN apt-get update -y --fix-missing
RUN apt-get install -y git build-essential curl wget ffmpeg unzip git git-lfs sox libsox-dev && \
    apt-get clean && \
    git lfs install

WORKDIR /workspace    

    # Install uv
# Ref: https://docs.astral.sh/uv/guides/integration/docker/#installing-uv
COPY --from=ghcr.io/astral-sh/uv:0.5.11 /uv /uvx /bin/
    
# Place executables in the environment at the front of the path
# Ref: https://docs.astral.sh/uv/guides/integration/docker/#using-the-environment
ENV PATH="/workspace/.venv/bin:$PATH"
    
# Compile bytecode
# Ref: https://docs.astral.sh/uv/guides/integration/docker/#compiling-bytecode
ENV UV_COMPILE_BYTECODE=1
    
# uv Cache
# Ref: https://docs.astral.sh/uv/guides/integration/docker/#caching
ENV UV_LINK_MODE=copy

# Copy project files first
COPY ./pyproject.toml ./uv.lock ./.python-version /workspace/ 
    
# Install dependencies with uv, which will use the Python version specified in .python-version
# Ref: https://docs.astral.sh/uv/guides/integration/docker/#intermediate-layers
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-install-project

ENV PYTHONPATH=/workspace

COPY ./pyproject.toml ./uv.lock /workspace/
COPY ./funasr /workspace/funasr
COPY ./tasks /workspace/tasks

COPY ./conf /workspace/conf
COPY ./config.py /workspace/config.py

COPY ./start_executor_worker.py /workspace/start_executor_worker.py
COPY ./start_task_paraformer.py /workspace/start_task_paraformer.py

RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync

CMD ["python", "./start_executor_worker.py"]
